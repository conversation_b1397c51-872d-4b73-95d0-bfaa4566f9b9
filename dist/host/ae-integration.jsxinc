/**
 * SahAI CEP Extension V2 - ExtendScript Main File
 * This file handles communication between the CEP panel and Adobe applications
 */

// Import constants
// @ts-nocheck
#include "constants.jsx"

// Global namespace for SahAI ExtendScript functions
var SahAI = SahAI || {};

/**
 * Initialize the ExtendScript environment
 */
SahAI.init = function() {
    try {
        // Set up error handling
        $.level = 1; // Enable debugging
        
        // Log initialization
        $.writeln("SahAI ExtendScript initialized successfully");
        
        return {
            success: true,
            message: "ExtendScript initialized",
            version: "2.0.0"
        };
    } catch (error) {
        $.writeln("Error initializing SahAI ExtendScript: " + error.toString());
        return {
            success: false,
            message: error.toString()
        };
    }
};

/**
 * Get application information
 */
SahAI.getAppInfo = function() {
    try {
        return {
            success: true,
            data: {
                name: app.name,
                version: app.version,
                locale: app.locale,
                build: app.build || "Unknown"
            }
        };
    } catch (error) {
        return {
            success: false,
            message: error.toString()
        };
    }
};

/**
 * Execute code in the host application
 * @param {string} code - The code to execute
 * @param {string} language - The programming language (for context)
 */
SahAI.executeCode = function(code, language) {
    try {
        var result;
        
        switch (language.toLowerCase()) {
            case 'javascript':
            case 'extendscript':
                // Execute ExtendScript code
                result = eval(code);
                break;
                
            case 'applescript':
                // Execute AppleScript (macOS only)
                if ($.os.indexOf("Mac") !== -1) {
                    result = app.doScript(code, ScriptLanguage.APPLESCRIPT_LANGUAGE);
                } else {
                    throw new Error("AppleScript is only supported on macOS");
                }
                break;
                
            case 'vbscript':
                // Execute VBScript (Windows only)
                if ($.os.indexOf("Win") !== -1) {
                    result = app.doScript(code, ScriptLanguage.VISUAL_BASIC);
                } else {
                    throw new Error("VBScript is only supported on Windows");
                }
                break;
                
            default:
                throw new Error("Unsupported language: " + language);
        }
        
        return {
            success: true,
            result: result ? result.toString() : "Code executed successfully",
            language: language
        };
    } catch (error) {
        return {
            success: false,
            message: error.toString(),
            language: language
        };
    }
};

/**
 * Get document information
 */
SahAI.getDocumentInfo = function() {
    try {
        if (!app.activeDocument) {
            return {
                success: false,
                message: "No active document"
            };
        }
        
        var doc = app.activeDocument;
        return {
            success: true,
            data: {
                name: doc.name,
                path: doc.fullName ? doc.fullName.toString() : "Unsaved",
                saved: doc.saved,
                modified: doc.modified || false
            }
        };
    } catch (error) {
        return {
            success: false,
            message: error.toString()
        };
    }
};

/**
 * Show alert dialog
 * @param {string} message - The message to display
 * @param {string} title - The dialog title
 */
SahAI.showAlert = function(message, title) {
    try {
        title = title || "SahAI";
        alert(message, title);
        return {
            success: true,
            message: "Alert displayed"
        };
    } catch (error) {
        return {
            success: false,
            message: error.toString()
        };
    }
};

/**
 * Log message to ExtendScript console
 * @param {string} message - The message to log
 * @param {string} level - Log level (info, warn, error)
 */
SahAI.log = function(message, level) {
    try {
        level = level || "info";
        var timestamp = new Date().toISOString();
        var logMessage = "[" + timestamp + "] [" + level.toUpperCase() + "] " + message;
        
        $.writeln(logMessage);
        
        return {
            success: true,
            message: "Logged: " + message
        };
    } catch (error) {
        return {
            success: false,
            message: error.toString()
        };
    }
};

/**
 * Get system information
 */
SahAI.getSystemInfo = function() {
    try {
        return {
            success: true,
            data: {
                os: $.os,
                version: $.version,
                buildDate: $.buildDate,
                locale: $.locale,
                memoryUsage: $.memCache
            }
        };
    } catch (error) {
        return {
            success: false,
            message: error.toString()
        };
    }
};

/**
 * Load settings from file
 */
function loadSettings() {
    try {
        var settingsFile = new File(SETTINGS_FILE_PATH);
        if (settingsFile.exists) {
            settingsFile.open("r");
            var content = settingsFile.read();
            settingsFile.close();
            return content;
        }
        return "{}";
    } catch (error) {
        $.writeln("Error loading settings: " + error.toString());
        return "{}";
    }
}

/**
 * Save settings to file
 * @param {Object} settings - Settings object to save
 */
function saveSettings(settings) {
    try {
        var settingsDir = new Folder(EXTENSION_DIR_PATH);
        if (!settingsDir.exists) {
            settingsDir.create();
        }

        var settingsFile = new File(SETTINGS_FILE_PATH);
        settingsFile.open("w");
        settingsFile.write(JSON.stringify(settings));
        settingsFile.close();

        return JSON.stringify({ success: true });
    } catch (error) {
        $.writeln("Error saving settings: " + error.toString());
        return JSON.stringify({ success: false, message: error.toString() });
    }
}

/**
 * Load chat history from file
 */
function loadHistory() {
    try {
        var historyFile = new File(HISTORY_FILE_PATH);
        if (historyFile.exists) {
            historyFile.open("r");
            var content = historyFile.read();
            historyFile.close();
            return JSON.stringify({ success: true, data: JSON.parse(content) });
        }
        return JSON.stringify({ success: true, data: [] });
    } catch (error) {
        $.writeln("Error loading history: " + error.toString());
        return JSON.stringify({ success: false, message: error.toString(), data: [] });
    }
}

/**
 * Save chat history to file
 * @param {Array} history - Array of chat sessions to save
 */
function saveHistory(history) {
    try {
        var settingsDir = new Folder(EXTENSION_DIR_PATH);
        if (!settingsDir.exists) {
            settingsDir.create();
        }

        var historyFile = new File(HISTORY_FILE_PATH);
        historyFile.open("w");
        historyFile.write(JSON.stringify(history));
        historyFile.close();

        return JSON.stringify({ success: true });
    } catch (error) {
        $.writeln("Error saving history: " + error.toString());
        return JSON.stringify({ success: false, message: error.toString() });
    }
}

/**
 * Get OpenAI model description
 * @param {string} modelId - Model identifier
 * @returns {string} Model description
 */
function getOpenAIModelDescription(modelId) {
    var descriptions = {
        'gpt-4o': 'Most capable OpenAI model',
        'gpt-4o-mini': 'Faster, more affordable',
        'gpt-4-turbo': 'Previous generation flagship',
        'gpt-3.5-turbo': 'Legacy model'
    };
    return descriptions[modelId] || '';
}

/**
 * Get OpenAI model context length
 * @param {string} modelId - Model identifier
 * @returns {number} Context length
 */
function getOpenAIModelContextLength(modelId) {
    var contextLengths = {
        'gpt-4o': 128000,
        'gpt-4o-mini': 128000,
        'gpt-4-turbo': 128000,
        'gpt-3.5-turbo': 16384
    };
    return contextLengths[modelId] || 4096;
}

/**
 * Get Groq model description
 * @param {string} modelId - Model identifier
 * @returns {string} Model description
 */
function getGroqModelDescription(modelId) {
    var descriptions = {
        'llama-3.1-8b-instant': 'Fast inference',
        'llama-3.1-70b-versatile': 'Balanced performance',
        'mixtral-8x7b-32768': 'Large context'
    };
    return descriptions[modelId] || '';
}

/**
 * Get Groq model context length
 * @param {string} modelId - Model identifier
 * @returns {number} Context length
 */
function getGroqModelContextLength(modelId) {
    var contextLengths = {
        'llama-3.1-8b-instant': 131072,
        'llama-3.1-70b-versatile': 131072,
        'mixtral-8x7b-32768': 32768
    };
    return contextLengths[modelId] || 8192;
}

/**
 * Get Mistral model description
 * @param {string} modelId - Model identifier
 * @returns {string} Model description
 */
function getMistralModelDescription(modelId) {
    var descriptions = {
        'mistral-large-latest': 'Most capable',
        'mistral-medium-latest': 'Balanced',
        'mistral-small-latest': 'Fast and efficient'
    };
    return descriptions[modelId] || '';
}

/**
 * Get Mistral model context length
 * @param {string} modelId - Model identifier
 * @returns {number} Context length
 */
function getMistralModelContextLength(modelId) {
    var contextLengths = {
        'mistral-large-latest': 128000,
        'mistral-medium-latest': 32000,
        'mistral-small-latest': 32000
    };
    return contextLengths[modelId] || 32000;
}

/**
 * List models for different providers
 * @param {string} providerId - Provider identifier
 * @param {string} baseURL - Base URL for the provider
 * @param {string} apiKey - API key for the provider
 */
function listModels(providerId, baseURL, apiKey) {
    try {
        var responseBody;
        var models = [];
        var retries = 3;

        function fetchWithRetry(fetchFn) {
            try {
                return fetchFn();
            } catch (e) {
                if (retries > 0) {
                    retries--;
                    $.sleep(1000); // Retry after 1s (ExtendScript sleep)
                    return fetchWithRetry(fetchFn);
                }
                throw e;
            }
        }

        switch (providerId) {
            case 'ollama':
                // baseURL is like 'http://localhost:11434'
                var ollamaHost = baseURL.replace('http://', '').split(':')[0];
                var ollamaPort = parseInt(baseURL.split(':')[2] || '11434', 10);
                responseBody = fetchWithRetry(function() {
                    return makeRequest(ollamaHost, '/api/tags', 'GET', null, ollamaPort);
                });
                var ollamaData = JSON.parse(responseBody);
                // Handle "blob style" models with rich metadata
                if (ollamaData && ollamaData.models) {
                    for (var i = 0; i < ollamaData.models.length; i++) {
                        models.push({
                            id: ollamaData.models[i].name,
                            name: ollamaData.models[i].name,
                            description: 'Size: ' + (ollamaData.models[i].size / 1e9).toFixed(2) + ' GB',
                            contextLength: ollamaData.models[i].details ? ollamaData.models[i].details.parameter_size : 0
                        });
                    }
                }
                // If no models, attempt blob pull (default: llama2:latest)
                if (models.length === 0) {
                    fetchWithRetry(function() {
                        return makeRequest(ollamaHost, '/api/pull', 'POST', null, ollamaPort, JSON.stringify({name: 'llama2:latest'}));
                    });
                    // Reload after pull
                    responseBody = fetchWithRetry(function() {
                        return makeRequest(ollamaHost, '/api/tags', 'GET', null, ollamaPort);
                    });
                    ollamaData = JSON.parse(responseBody);
                    if (ollamaData && ollamaData.models) {
                        for (var j = 0; j < ollamaData.models.length; j++) {
                            models.push({
                                id: ollamaData.models[j].name,
                                name: ollamaData.models[j].name,
                                description: 'Size: ' + (ollamaData.models[j].size / 1e9).toFixed(2) + ' GB',
                                contextLength: ollamaData.models[j].details ? ollamaData.models[j].details.parameter_size : 0
                            });
                        }
                    }
                }
                break;

            case 'openai':
            case 'groq': // Groq uses an OpenAI-compatible endpoint
                var host = (providerId === 'groq') ? 'api.groq.com' : 'api.openai.com';
                var path = (providerId === 'groq') ? '/openai/v1/models' : '/v1/models';
                responseBody = fetchWithRetry(function() {
                    return makeRequest(host, path, 'GET', apiKey);
                });
                var openAIData = JSON.parse(responseBody);
                if (openAIData && openAIData.data) {
                    for (var j = 0; j < openAIData.data.length; j++) {
                        models.push({
                            id: openAIData.data[j].id,
                            name: openAIData.data[j].id,
                            description: providerId === 'openai' ? getOpenAIModelDescription(openAIData.data[j].id) : getGroqModelDescription(openAIData.data[j].id),
                            contextLength: providerId === 'openai' ? getOpenAIModelContextLength(openAIData.data[j].id) : getGroqModelContextLength(openAIData.data[j].id),
                            isRecommended: providerId === 'openai' ? (openAIData.data[j].id === 'gpt-4o' || openAIData.data[j].id === 'gpt-4o-mini') : openAIData.data[j].id.indexOf('llama-3.1-70b') !== -1
                        });
                    }
                }
                break;

            case 'anthropic':
                // Anthropic doesn't have a public models endpoint.
                // Return a hardcoded list of common models as fallback.
                models = [
                    { id: 'claude-3-5-sonnet-20241022', name: 'Claude 3.5 Sonnet', description: 'Most capable model', contextLength: 200000, isRecommended: true },
                    { id: 'claude-3-5-haiku-20241022', name: 'Claude 3.5 Haiku', description: 'Fast and efficient', contextLength: 200000, isRecommended: false },
                    { id: 'claude-3-opus-20240229', name: 'Claude 3 Opus', description: 'Powerful reasoning', contextLength: 200000, isRecommended: false },
                    { id: 'claude-2.1', name: 'Claude 2.1', description: 'Previous generation', contextLength: 200000, isRecommended: false }
                ];
                break;

            // Add cases for other providers like 'gemini', 'mistral', etc.
            case 'gemini':
                responseBody = fetchWithRetry(function() {
                    return makeRequest('generativelanguage.googleapis.com', '/v1beta/models?key=' + apiKey, 'GET');
                });
                var geminiData = JSON.parse(responseBody);
                if (geminiData && geminiData.models) {
                    for (var k = 0; k < geminiData.models.length; k++) {
                        models.push({
                            id: geminiData.models[k].name.replace('models/', ''),
                            name: geminiData.models[k].displayName || geminiData.models[k].name,
                            description: geminiData.models[k].description || 'Google Gemini model',
                            contextLength: geminiData.models[k].inputTokenLimit || 1000000,
                            isRecommended: geminiData.models[k].name.indexOf('gemini-1.5-pro') !== -1
                        });
                    }
                } else {
                    // Fallback models for Gemini
                    models = [
                        { id: 'gemini-1.5-pro', name: 'Gemini 1.5 Pro', description: 'Most capable model', contextLength: 2000000, isRecommended: true },
                        { id: 'gemini-1.5-flash', name: 'Gemini 1.5 Flash', description: 'Fast and efficient', contextLength: 1000000, isRecommended: false }
                    ];
                }
                break;

            default:
                throw new Error("Unsupported provider: " + providerId);
        }

        return JSON.stringify(models);

    } catch (e) {
        // Return error information to the client for better debugging
        return JSON.stringify({ error: true, message: e.toString(), stack: e.stack });
    }


/**
 * Get fallback models when API calls fail
 * @param {string} providerId - Provider identifier
 * @returns {Array} Array of fallback models
 */
function getFallbackModels(providerId) {
    var fallbackModels = {
        'openai': [
            { id: 'gpt-4o', name: 'GPT-4o', description: 'Most capable OpenAI model', context_length: 128000, is_recommended: true },
            { id: 'gpt-4o-mini', name: 'GPT-4o Mini', description: 'Faster, more affordable', context_length: 128000, is_recommended: false },
            { id: 'gpt-4-turbo', name: 'GPT-4 Turbo', description: 'Previous generation flagship', context_length: 128000, is_recommended: false },
            { id: 'gpt-3.5-turbo', name: 'GPT-3.5 Turbo', description: 'Legacy model', context_length: 16384, is_recommended: false }
        ],
        'anthropic': [
            { id: 'claude-3-5-sonnet-20241022', name: 'Claude 3.5 Sonnet', description: 'Anthropic\'s most capable model', context_length: 200000, is_recommended: true },
            { id: 'claude-3-5-haiku-20241022', name: 'Claude 3.5 Haiku', description: 'Fast and efficient', context_length: 200000, is_recommended: false },
            { id: 'claude-3-opus-20240229', name: 'Claude 3 Opus', description: 'Powerful reasoning', context_length: 200000, is_recommended: false }
        ],
        'gemini': [
            { id: 'gemini-1.5-pro', name: 'Gemini 1.5 Pro', description: 'Google\'s most capable model', context_length: 2000000, is_recommended: true },
            { id: 'gemini-1.5-flash', name: 'Gemini 1.5 Flash', description: 'Fast and efficient', context_length: 1000000, is_recommended: false },
            { id: 'gemini-1.0-pro', name: 'Gemini 1.0 Pro', description: 'Previous generation', context_length: 32000, is_recommended: false }
        ],
        'groq': [
            { id: 'llama-3.1-70b-versatile', name: 'Llama 3.1 70B', description: 'Balanced performance', context_length: 131072, is_recommended: true },
            { id: 'llama-3.1-8b-instant', name: 'Llama 3.1 8B', description: 'Fast inference', context_length: 131072, is_recommended: false },
            { id: 'mixtral-8x7b-32768', name: 'Mixtral 8x7B', description: 'Large context', context_length: 32768, is_recommended: false }
        ],
        'deepseek': [
            { id: 'deepseek-chat', name: 'DeepSeek Chat', description: 'General purpose', context_length: 128000, is_recommended: true },
            { id: 'deepseek-coder', name: 'DeepSeek Coder', description: 'Code-focused', context_length: 128000, is_recommended: false }
        ],
        'mistral': [
            { id: 'mistral-large-latest', name: 'Mistral Large', description: 'Most capable', context_length: 128000, is_recommended: true },
            { id: 'mistral-medium-latest', name: 'Mistral Medium', description: 'Balanced', context_length: 32000, is_recommended: false },
            { id: 'mistral-small-latest', name: 'Mistral Small', description: 'Fast and efficient', context_length: 32000, is_recommended: false }
        ],
        'moonshot': [
            { id: 'moonshot-v1-8k', name: 'Moonshot v1 8K', description: 'Small context', context_length: 8000, is_recommended: false },
            { id: 'moonshot-v1-32k', name: 'Moonshot v1 32K', description: 'Medium context', context_length: 32000, is_recommended: false },
            { id: 'moonshot-v1-128k', name: 'Moonshot v1 128K', description: 'Large context', context_length: 128000, is_recommended: true }
        ],
        'openrouter': [
            { id: 'openai/gpt-4o', name: 'GPT-4o (OpenRouter)', description: 'OpenAI via OpenRouter', context_length: 128000, is_recommended: true },
            { id: 'anthropic/claude-3.5-sonnet', name: 'Claude 3.5 Sonnet (OpenRouter)', description: 'Anthropic via OpenRouter', context_length: 200000, is_recommended: false },
            { id: 'meta-llama/llama-3.1-70b-instruct', name: 'Llama 3.1 70B (OpenRouter)', description: 'Meta via OpenRouter', context_length: 131072, is_recommended: false }
        ],
        'perplexity': [
            { id: 'llama-3.1-sonar-small-128k-online', name: 'Llama 3.1 Sonar Small 128K Online', description: 'Small online model', context_length: 128000, is_recommended: false },
            { id: 'llama-3.1-sonar-large-128k-online', name: 'Llama 3.1 Sonar Large 128K Online', description: 'Large online model', context_length: 128000, is_recommended: true },
            { id: 'llama-3.1-sonar-huge-128k-online', name: 'Llama 3.1 Sonar Huge 128K Online', description: 'Huge online model', context_length: 128000, is_recommended: false }
        ],
        'qwen': [
            { id: 'qwen-turbo', name: 'Qwen Turbo', description: 'Fast and efficient', context_length: 8000, is_recommended: false },
            { id: 'qwen-plus', name: 'Qwen Plus', description: 'Balanced performance', context_length: 32000, is_recommended: false },
            { id: 'qwen-max', name: 'Qwen Max', description: 'Most capable', context_length: 32000, is_recommended: true }
        ],
        'together': [
            { id: 'meta-llama/Llama-3-70b-chat-hf', name: 'Llama 3 70B Chat', description: 'Large language model', context_length: 8192, is_recommended: true },
            { id: 'meta-llama/Llama-3-8b-chat-hf', name: 'Llama 3 8B Chat', description: 'Smaller, faster model', context_length: 8192, is_recommended: false },
            { id: 'mistralai/Mixtral-8x7B-Instruct-v0.1', name: 'Mixtral 8x7B Instruct', description: 'Mixture of experts', context_length: 32768, is_recommended: false }
        ],
        'vertex': [
            { id: 'gemini-1.5-pro', name: 'Gemini 1.5 Pro', description: 'Google\'s most capable model', context_length: 2000000, is_recommended: true },
            { id: 'gemini-1.5-flash', name: 'Gemini 1.5 Flash', description: 'Fast and efficient', context_length: 1000000, is_recommended: false },
            { id: 'gemini-1.0-pro', name: 'Gemini 1.0 Pro', description: 'Previous generation', context_length: 32000, is_recommended: false }
        ],
        'xai': [
            { id: 'grok-beta', name: 'Grok Beta', description: 'xAI\'s flagship model', context_length: 128000, is_recommended: true },
            { id: 'grok-vision-beta', name: 'Grok Vision Beta', description: 'Vision-capable model', context_length: 128000, is_recommended: false }
        ],
        'ollama': [
            { id: 'llama3.1', name: 'Llama 3.1', description: 'Open source LLM', context_length: 4096, is_recommended: true },
            { id: 'mistral', name: 'Mistral', description: 'Efficient transformer', context_length: 8192, is_recommended: false },
            { id: 'codellama', name: 'Code Llama', description: 'Code-focused', context_length: 16384, is_recommended: false }
        ],
        'lmstudio': [
            { id: 'local-model', name: 'Local Model', description: 'Your local model', context_length: 4096, is_recommended: true }
        ]
    };

    return fallbackModels[providerId] || [];
}

/**
 * Makes an HTTP request using ExtendScript's Socket object.
 * @param {string} host The domain name (e.g., 'api.openai.com').
 * @param {string} path The API path (e.g., '/v1/models').
 * @param {string} method The HTTP method ('GET', 'POST', etc.).
 * @param {string} [apiKey] The Bearer token API key.
 * @param {number} [port=443] The port number (443 for HTTPS).
 * @param {string} [body] The request body for POST requests.
 * @returns {string} The raw JSON response string.
 */
function makeRequest(host, path, method, apiKey, port, body) {
    port = port || 443;
    var conn = new Socket();
    var responseBody = '';

    if (conn.open(host + ':' + port, 'UTF-8', undefined, true)) {
        var headers = [
            method + ' ' + path + ' HTTP/1.1',
            'Host: ' + host,
            'Content-Type: application/json',
            'User-Agent: SahAI-CEP-Extension/2.0',
            'Accept: application/json',
            'Cache-Control: no-cache'
        ];

        if (apiKey) {
            headers.push('Authorization: Bearer ' + apiKey);
        }

        // Add content length for POST requests
        if (body && method === 'POST') {
            headers.push('Content-Length: ' + body.length);
        }

        // Send headers
        conn.write(headers.join('\r\n') + '\r\n\r\n');

        // Send body if present
        if (body && method === 'POST') {
            conn.write(body);
        }

        var response = conn.read();
        conn.close();

        // Extract the JSON body from the HTTP response
        var bodyStartIndex = response.indexOf('\r\n\r\n');
        if (bodyStartIndex > -1) {
            responseBody = response.substring(bodyStartIndex + 4);
        } else {
            // Fallback for responses without headers (e.g., some Ollama setups)
            bodyStartIndex = response.indexOf('{');
            var bodyEndIndex = response.lastIndexOf('}');
            if(bodyStartIndex > -1 && bodyEndIndex > -1) {
               responseBody = response.substring(bodyStartIndex, bodyEndIndex + 1);
            }
        }
        return responseBody;
    }
    throw new Error('Failed to connect to ' + host);
}

/**
 * Helper function to make HTTP requests with timeout and better error handling
 * @param {string} url - URL to fetch
 * @param {Object} headers - Optional headers
 * @param {number} timeout - Timeout in milliseconds (default: 10000)
 * @param {number} retries - Number of retry attempts (default: 2)
 */
function getURL(url, headers, timeout, retries) {
    timeout = timeout || 10000; // Default 10 second timeout (reduced for better UX)
    retries = retries || 2; // Default 2 retries

    var lastError = null;

    for (var attempt = 0; attempt <= retries; attempt++) {
        try {
            $.writeln("Making HTTP request to: " + url + " (attempt " + (attempt + 1) + "/" + (retries + 1) + ")");

            var http = new XMLHttpRequest();
            var startTime = new Date().getTime();

            // Enhanced timeout handling for ExtendScript
            var timeoutId = null;
            var timedOut = false;

            // Manual timeout implementation since XMLHttpRequest.timeout may not work
            if (timeout > 0) {
                timeoutId = setTimeout(function() {
                    timedOut = true;
                    try {
                        http.abort();
                    } catch (e) {
                        // Ignore abort errors
                    }
                }, timeout);
            }

            http.open("GET", url, false); // Synchronous request

            // Set default headers with better compatibility
            try {
                http.setRequestHeader("User-Agent", "SahAI-CEP-Extension/2.0");
                http.setRequestHeader("Accept", "application/json");
                http.setRequestHeader("Cache-Control", "no-cache");

                // Add CORS headers for local providers
                if (url.indexOf('localhost') !== -1 || url.indexOf('127.0.0.1') !== -1) {
                    http.setRequestHeader("Access-Control-Allow-Origin", "*");
                }
            } catch (headerError) {
                $.writeln("Warning: Could not set some headers: " + headerError.toString());
            }

            // Set custom headers
            if (headers) {
                for (var key in headers) {
                    if (headers.hasOwnProperty(key)) {
                        try {
                            http.setRequestHeader(key, headers[key]);
                        } catch (headerError) {
                            $.writeln("Warning: Could not set header " + key + ": " + headerError.toString());
                        }
                    }
                }
            }

            // Send request
            http.send();

            // Clear timeout
            if (timeoutId) {
                clearTimeout(timeoutId);
            }

            // Check if request timed out
            if (timedOut) {
                throw new Error("Request timed out after " + timeout + "ms");
            }

            var endTime = new Date().getTime();
            var duration = endTime - startTime;
            $.writeln("HTTP response status: " + http.status + " (took " + duration + "ms)");

            if (http.status >= 200 && http.status < 300) {
                return { success: true, data: http.responseText, duration: duration };
            } else if (http.status === 401) {
                return { success: false, message: "Unauthorized - Invalid API key" };
            } else if (http.status === 403) {
                return { success: false, message: "Forbidden - Access denied" };
            } else if (http.status === 404) {
                return { success: false, message: "Not Found - API endpoint not found" };
            } else if (http.status === 429) {
                return { success: false, message: "Rate Limited - Too many requests" };
            } else if (http.status >= 500) {
                return { success: false, message: "Server Error - Please try again later" };
            } else {
                throw new Error("HTTP " + http.status + " - " + http.statusText);
            }
        } catch (error) {
            lastError = error;
            var errorMessage = error.toString();
            $.writeln("HTTP request error (attempt " + (attempt + 1) + "): " + errorMessage);

            // Don't retry on certain errors
            if (errorMessage.indexOf('401') !== -1 || errorMessage.indexOf('403') !== -1 ||
                errorMessage.indexOf('Unauthorized') !== -1 || errorMessage.indexOf('Forbidden') !== -1) {
                break; // Don't retry auth errors
            }

      // Wait before retry (exponential backoff)
      if (attempt < retries) {
        var delay = Math.min(1000 * Math.pow(2, attempt), 3000); // Max 3s delay
        $.writeln("Retrying in " + delay + "ms...");

        // Use a more efficient delay mechanism
        var startDelay = new Date().getTime();
        var currentDelay = 0;
        while (currentDelay < delay) {
          // Small sleep to prevent blocking the thread
          $.sleep(10);
          currentDelay = new Date().getTime() - startDelay;
        }
      }
        }
    }

    // All attempts failed
    var errorMessage = lastError ? lastError.toString() : "Unknown error";
    if (errorMessage.indexOf('timeout') !== -1 || errorMessage.indexOf('timed out') !== -1) {
        return { success: false, message: "Request timed out after " + timeout + "ms" };
    } else if (errorMessage.indexOf('network') !== -1 || errorMessage.indexOf('connection') !== -1) {
        return { success: false, message: "Network connection error" };
    } else {
        return { success: false, message: errorMessage };
    }
}

/**
 * Fetch models for a specific provider (wrapper for listModels)
 * This function is called by the ProviderBridge in cepIntegration.ts
 * @param {string} providerId - Provider identifier
 * @param {string} baseURL - Base URL for the provider
 * @param {string} apiKey - API key for the provider
 */
function fetchModels(providerId, baseURL, apiKey) {
    try {
        $.writeln("fetchModels called with providerId: " + providerId + ", baseURL: " + (baseURL || 'default') + ", apiKey: " + (apiKey ? 'provided' : 'not provided'));

        // Call the existing listModels function
        var result = listModels(providerId, baseURL, apiKey);
        var parsedResult = JSON.parse(result);

        // Transform the result to match expected format
        if (parsedResult.ok) {
            return JSON.stringify(parsedResult.models);
        } else {
            return JSON.stringify({ error: parsedResult.error || 'Failed to fetch models' });
        }
    } catch (error) {
        $.writeln("Error in fetchModels: " + error.toString());
        return JSON.stringify({ error: error.toString() });
    }
}

// Initialize SahAI when script loads
try {
    $.writeln("=== SahAI Extension Loading ===");
    SahAI.init();
    $.writeln("=== SahAI Extension Loaded Successfully ===");
} catch (error) {
    $.writeln("=== SahAI Extension Load Error: " + error.toString() + " ===");
}
